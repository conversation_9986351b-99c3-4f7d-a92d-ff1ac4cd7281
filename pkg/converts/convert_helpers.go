package converts

import "unsafe"

// 文本类型到字符串的零拷贝转换 - 高性能优化版本
func textualToString[S Textual](src S) string {
	switch s := any(src).(type) {
	case string:
		return s
	case []byte:
		if len(s) == 0 {
			return ""
		}
		return unsafe.String(unsafe.SliceData(s), len(s))
	case []rune:
		return string(s)
	default:
		return ""
	}
}

// 字符串到文本类型的零拷贝转换 - 高性能优化版本
func stringToTextual[D Textual](s string) D {
	var zero D
	switch any(zero).(type) {
	case string:
		return any(s).(D)
	case []byte:
		if len(s) == 0 {
			return any([]byte(nil)).(D)
		}
		return any(unsafe.Slice(unsafe.StringData(s), len(s))).(D)
	case []rune:
		return any([]rune(s)).(D)
	default:
		return zero
	}
}

func isInt[T Numeric](src T) bool {
	return src >= 0
}

func isUint[T Numeric](src T) bool {
	return src >= 0
}

func isFloat[T Numeric](src T) bool {
	return src != 0
}
