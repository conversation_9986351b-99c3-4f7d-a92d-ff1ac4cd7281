package converts

import (
	"reflect"
	"strconv"
)

type (
	Boolean interface {
		~bool
	}

	Numeric interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr | ~float32 | ~float64
	}

	Complex interface {
		~complex64 | ~complex128
	}

	Textual interface {
		~string | ~[]byte | ~[]rune
	}
)

// ====================================================================================================================

func booleanToBoolean[S Boolean, D Boolean](src S) (D, error) {
	return D(src), nil
}

func numericToBoolean[S Numeric, D Boolean](src S) (D, error) {
	if src != 0 {
		return D(true), nil
	}
	return D(false), nil
}

func complexToBoolean[S Complex, D Boolean](src S) (D, error) {
	if src != 0 {
		return D(true), nil
	}
	return D(false), nil
}

func textualToBoolean[S Textual, D Boolean](src S) (D, error) {
	if textualToString(src) != "" {
		return D(true), nil
	}
	return D(false), nil
}

// ====================================================================================================================

func booleanToNumeric[S Boolean, D Numeric](src S) (D, error) {
	if bool(src) {
		return D(1), nil
	}
	return D(0), nil
}

func numericToNumeric[S Numeric, D Numeric](src S) (D, error) {
	return D(src), nil
}

func complexToNumeric[S Complex, D Numeric](src S) (D, error) {
	return D(real(complex128(src))), nil
}

func textualToNumeric[S Textual, D Numeric](src S) (D, error) {
	switch {
	case isInt[D]():
		if i, err := strconv.ParseInt(textualToString(src), 10, getBitSize[D]()); err == nil {
			return D(i), nil
		}
	case isUint[D]():
		if u, err := strconv.ParseUint(textualToString(src), 10, getBitSize[D]()); err == nil {
			return D(u), nil
		}
	case isFloat[D]():
		if f, err := strconv.ParseFloat(textualToString(src), getBitSize[D]()); err == nil {
			return D(f), nil
		}
	}
	return D(0), nil
}

// ====================================================================================================================
func booleanToComplex[S Boolean, D Complex](src S) (D, error) {
	if bool(src) {
		return D(complex(1, 0)), nil
	}
	return D(complex(0, 0)), nil
}

func numericToComplex[S Numeric, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

func complexToComplex[S Complex, D Complex](src S) (D, error) {
	return D(src), nil
}

func textualToComplex[S Textual, D Complex](src S) (D, error) {
	if i, err := strconv.ParseComplex(textualToString(src), getBitSize[D]()); err == nil {
		return D(i), nil
	}
	return D(complex(0, 0)), nil
}

// ====================================================================================================================

func booleanToTextual[S Boolean, D Textual](src S) (D, error) {
	return D(strconv.FormatBool(bool(src))), nil
}

func numericToTextual[S Numeric, D Textual](src S) (D, error) {
	switch {
	case isInt[S]():
		return D(strconv.FormatInt(int64(src), 10)), nil
	case isUint[S]():
		return D(strconv.FormatUint(uint64(src), 10)), nil
	case isFloat[S]():
		return D(strconv.FormatFloat(float64(src), 'f', -1, getBitSize[S]())), nil
	}
	return D(""), nil
}

func complexToTextual[S Complex, D Textual](src S) (D, error) {
	return D(strconv.FormatComplex(complex128(src), 'f', -1, 64)), nil
}

func textualToTextual[S Textual, D Textual](src S) (D, error) {
	var srcZero S
	var dstZero D

	// 相同类型直接返回
	if reflect.TypeOf(srcZero) == reflect.TypeOf(dstZero) {
		return any(src).(D), nil
	}

	// 转换为字符串再转换为目标类型
	str := textualToString(src)
	return stringToTextual[D](str)
}
